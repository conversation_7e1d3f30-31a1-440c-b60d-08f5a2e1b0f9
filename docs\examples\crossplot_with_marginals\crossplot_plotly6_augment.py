# -*- coding: utf-8 -*-
"""
Production-Grade Cross-Plot Module using Plotly 6.0+

This module implements a comprehensive cross-plot visualization system following
the detailed specification document. It provides a robust, extensible architecture
for creating professional-grade cross-plots with marginal distributions.

## 核心开发要点

### 🎯 主要功能特性
- ✓ Pydantic-based configuration validation
- ✓ Extensible component registration system
- ✓ Multiple marginal plot types (hist, box, violin, kde)
- ✓ Advanced marginal plot configuration (Plotly 6.0原生功能)
- ✓ Dual colorbar support (多系列独立色轴)
- ✓ Logarithmic axis scaling
- ✓ Error bars and color mapping support
- ✓ Professional styling and theming
- ✓ Flexible reference lines with slope/intercept control
- ✓ Configurable marginal plot sizes and spacing
- ✓ Smart legend positioning to avoid colorbar overlap
- ✓ Consistent tick styling across main and marginal plots
- ✓ Configurable axis range modes (normal, tozero, nonnegative)
- ✓ Advanced tick control (major/minor ticks with independent styling)

### 🔧 边缘图高级配置 (Plotly 6.0原生功能)
- **箱线图**: 数据点显示、均值线、填充色控制
- **小提琴图**: 内部箱线图、均值线、侧面显示控制
- **KDE**: 基于Violin的平滑密度估计
- **颜色联动**: 与主图图例交互同步

### 🎨 双色轴支持 (符合《交会图绘制要求》规范)
- 每个系列可配置独立的颜色映射和色轴
- 支持不同物理量的同时可视化
- 垂直居中显示，专业美观

### 📏 参考线功能
- 可定制斜率和截距 (y = mx + b)
- 支持1:1线、回归线或任意线性参考
- 可配置颜色、宽度和线型
- 可选图例显示
- 向后兼容布尔配置

### 📊 边缘图配置最佳实践
- size_x: X轴边缘图高度占比 (默认0.15, 推荐0.08-0.20)
- size_y: Y轴边缘图宽度占比 (默认0.15, 推荐0.08-0.20)
- spacing: 边缘图与主图间距 (默认0.01)
- 更小的尺寸可以给主图更多空间，提高数据可读性

### 🎛️ 坐标轴范围模式 (Plotly内置功能)
- range_mode='normal': 自动范围，包含适当空白 (默认)
- range_mode='tozero': 强制包含零点，减少空白
- range_mode='nonnegative': 非负范围，适用于物理量
- **业务最佳实践**:
  - 科学数据分析: 推荐'tozero'，确保坐标轴从零开始
  - 演示报告: 使用'normal'，视觉效果更好
  - 物理量数据: 使用'nonnegative'，避免负值显示
  - 对数坐标: 自动处理，无需手动设置范围

### ⚙️ 高级刻度控制 (Plotly 6.0原生功能)
- 主刻度控制: 间隔、起始位置、长度、宽度、颜色
- 次要刻度控制: 独立的样式配置，支持不同间隔和颜色
- 网格线控制: 主/次要网格线的颜色、宽度、样式
- 刻度模式: auto(自动)、linear(线性)、array(自定义)
- 示例: 100~1000范围，主刻度间隔100，次要刻度间隔50
- 完全基于Plotly内置功能，无需自定义实现

## 架构设计
- Configuration Object Pattern with Pydantic validation
- Component Registration System for extensibility
- Style Precedence Logic (Z-mapping > Series-specific > Global defaults)
- Manual subplot layout using make_subplots for full control
- Format-Agnostic Principle (FAP) - 格式无关架构设计
"""

import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
import itertools
from typing import List, Dict, Any, Optional, Union, Callable
from pydantic import BaseModel, Field, field_validator
from dataclasses import dataclass
import os
from abc import ABC, abstractmethod
from enum import Enum


# ==============================================================================
# Section: Configuration Models (Pydantic-based)
# ==============================================================================

class ScaleType(str, Enum):
    """Supported axis scale types (Plotly 6.0原生支持的类型)."""
    LINEAR = "linear"
    LOG = "log"  # Plotly原生支持，要求数据为正数
    # 注意：Plotly不支持symlog（对称对数）刻度
    # 如需symlog功能，请在数据预处理阶段进行变换


class SeriesType(str, Enum):
    """Supported series visualization types."""
    SCATTER = "scatter"
    LINE = "line"
    BOTH = "both"


class MarginalKind(str, Enum):
    """Supported marginal plot types (Plotly 6.0原生支持)."""
    HIST = "hist"
    BOX = "box"
    VIOLIN = "violin"
    KDE = "kde"  # 使用histogram with density实现
    # 注意：不支持CDF、CCDF、PROB、BOXEN等类型
    # 这些需要额外的数据处理或其他库支持


class MarginalEnabled(str, Enum):
    """Marginal plot enablement options."""
    NONE = "none"
    X = "x"
    Y = "y"
    BOTH = "both"


class TickConfig(BaseModel):
    """Configuration for axis ticks (功能性配置，样式在StyleConfig中)."""
    mode: str = "auto"  # "auto", "linear"
    interval: Optional[float] = None  # dtick - 主刻度间隔
    start: Optional[float] = None     # tick0 - 起始位置

    # 主刻度位置
    position: str = "outside"  # "inside", "outside", ""

    # 次要刻度配置
    minor_enabled: bool = False
    minor_interval: Optional[float] = None  # minor dtick
    minor_start: Optional[float] = None     # minor tick0
    minor_position: str = "outside"         # minor ticks position


class GridConfig(BaseModel):
    """Configuration for axis grid lines (功能性配置，样式在StyleConfig中)."""
    major_enabled: bool = True
    major_dash: str = "solid"  # "solid", "dot", "dash", "dashdot"

    minor_enabled: bool = False
    minor_dash: str = "dot"


class AxisTitleConfig(BaseModel):
    """Configuration for axis title."""
    text: str = "Axis Label"
    standoff: Optional[float] = None  # 标题与轴的距离
    side: str = "bottom"  # "top", "bottom", "left", "right"


class AxisRangeConfig(BaseModel):
    """Configuration for axis range."""
    mode: str = "normal"  # "normal", "tozero", "nonnegative"
    min_value: Optional[float] = None  # 手动设置最小值
    max_value: Optional[float] = None  # 手动设置最大值
    auto_margin: bool = True  # 自动边距
    constrain: Optional[str] = None  # "range", "domain"


class AxisConfig(BaseModel):
    """Configuration for a single axis."""
    title: AxisTitleConfig = Field(default_factory=AxisTitleConfig)
    scale: ScaleType = ScaleType.LINEAR
    log_base: Optional[float] = None
    inverted: bool = False
    range: AxisRangeConfig = Field(default_factory=AxisRangeConfig)

    # 刻度标签配置
    show_tick_labels: bool = True
    tick_angle: Optional[float] = None  # 刻度标签角度
    tick_prefix: str = ""  # 刻度标签前缀
    tick_suffix: str = ""  # 刻度标签后缀
    tick_format: Optional[str] = None  # d3格式字符串

    # 精细刻度和网格控制
    ticks: TickConfig = Field(default_factory=TickConfig)
    grid: GridConfig = Field(default_factory=GridConfig)


class LegendConfig(BaseModel):
    """Configuration for legend display."""
    enabled: bool = True
    position: str = "right"  # "right", "left", "top", "bottom"

    # 位置精确控制
    x: Optional[float] = None  # 0-1之间，相对位置
    y: Optional[float] = None  # 0-1之间，相对位置
    xanchor: str = "auto"  # "auto", "left", "center", "right"
    yanchor: str = "auto"  # "auto", "top", "middle", "bottom"

    # 布局控制
    orientation: str = "v"  # "v" (vertical), "h" (horizontal)
    itemsizing: str = "trace"  # "trace", "constant"
    itemwidth: Optional[float] = None  # 图例项宽度
    itemclick: str = "toggle"  # "toggle", "toggleothers", False
    itemdoubleclick: str = "toggleothers"  # "toggle", "toggleothers", False

    # 边框和背景
    borderwidth: float = 0
    bgcolor: Optional[str] = None  # 背景色，None表示透明

    # 分组
    groupclick: str = "toggleitem"  # "toggleitem", "togglegroup"
    grouptitlefont_size: Optional[float] = None


class MarkerConfig(BaseModel):
    """Configuration for scatter plot markers."""
    symbol: str = "circle"  # Plotly支持的所有符号
    size: Union[float, List[float]] = 8.0
    facecolor: Optional[str] = None
    linecolor: str = "white"
    edgewidth: float = 1.0

    # 高级标记配置
    opacity: Optional[float] = None
    sizemode: str = "diameter"  # "diameter", "area"
    sizeref: Optional[float] = None  # 标记大小参考值
    sizemin: Optional[float] = None  # 最小标记大小
    maxdisplayed: Optional[int] = None  # 最大显示数量


class LineConfig(BaseModel):
    """Configuration for line plots."""
    style: str = "solid"  # "solid", "dash", "dot", "dashdot", "longdash", "longdashdot"
    color: Optional[str] = None
    width: float = 2.0

    # 高级线条配置
    shape: str = "linear"  # "linear", "spline", "hv", "vh", "hvh", "vhv"
    smoothing: Optional[float] = None  # 0-1.3，仅用于spline
    simplify: bool = True  # 简化线条以提高性能
    connectgaps: bool = False  # 是否连接数据间隙


class HoverConfig(BaseModel):
    """Configuration for hover information."""
    enabled: bool = True
    mode: str = "closest"  # "closest", "x", "y", "x unified", "y unified"

    # 悬停模板
    template: Optional[str] = None  # 自定义悬停模板
    name: Optional[str] = None  # 悬停时显示的名称

    # 悬停标签配置
    bgcolor: Optional[str] = None  # 背景色
    bordercolor: Optional[str] = None  # 边框色
    align: str = "auto"  # "left", "right", "auto"

    # 悬停距离
    hoverdistance: int = 20  # 鼠标距离阈值
    spikedistance: int = 20  # 尖峰线距离阈值


class ErrorBarConfig(BaseModel):
    """Configuration for error bars."""
    visible: bool = False
    x_values: Optional[str] = None
    y_values: Optional[str] = None
    style: str = "symmetric"
    color: Optional[str] = None
    thickness: float = 1.0

    # 高级误差线配置
    type: str = "data"  # "data", "percent", "sqrt", "constant"
    symmetric: bool = True  # 是否对称
    array_minus: Optional[List[float]] = None  # 非对称误差的下限
    cap_size: float = 0  # 端点大小


class SeriesConfig(BaseModel):
    """Configuration for a data series."""
    id: str
    type: SeriesType = SeriesType.SCATTER
    marker: MarkerConfig = Field(default_factory=MarkerConfig)
    line: LineConfig = Field(default_factory=LineConfig)
    error_bars: ErrorBarConfig = Field(default_factory=ErrorBarConfig)
    hover: HoverConfig = Field(default_factory=HoverConfig)

    # 显示控制
    visible: bool = True
    alpha: float = 0.7
    zorder: int = 1

    # 图例配置
    showlegend: bool = True
    legendgroup: Optional[str] = None  # 图例分组
    legendrank: Optional[int] = None  # 图例排序

    # 填充配置
    fill: Optional[str] = None  # "none", "tozeroy", "tozerox", "tonexty", "tonextx", "toself", "tonext"
    fillcolor: Optional[str] = None

    # 独立颜色轴配置（用于双颜色轴等高级功能）
    colorbar: Optional["ColorbarConfig"] = None  # 如果设置，该系列使用独立的颜色轴
    colorscale: Optional[str] = None  # 该系列专用的颜色映射


class ColorbarConfig(BaseModel):
    """Configuration for color mapping."""
    visible: bool = False
    cmap: str = "Viridis"  # Plotly支持的所有colorscale
    title: str = "Z Value"
    orientation: str = "vertical"  # "vertical", "horizontal"

    # 位置和尺寸控制
    x: Optional[float] = None  # 0-1之间，相对位置
    y: Optional[float] = None  # 0-1之间，相对位置
    xanchor: str = "left"  # "left", "center", "right"
    yanchor: str = "bottom"  # "top", "middle", "bottom"
    len: float = 1.0  # 长度比例
    thickness: float = 30  # 厚度（像素）

    # 刻度控制
    tickmode: str = "auto"  # "auto", "linear", "array"
    tick0: Optional[float] = None  # 起始刻度
    dtick: Optional[float] = None  # 刻度间隔
    tickvals: Optional[List[float]] = None  # 自定义刻度位置
    ticktext: Optional[List[str]] = None  # 自定义刻度标签

    # 范围控制
    cmin: Optional[float] = None  # 最小值
    cmax: Optional[float] = None  # 最大值
    cmid: Optional[float] = None  # 中间值
    clim: Optional[List[float]] = None  # 兼容旧版本

    # 边框
    outlinecolor: Optional[str] = None  # 边框颜色
    outlinewidth: float = 1  # 边框宽度

    # R-12 数据清洗与无效值处理
    nan_color: Optional[str] = None  # Z轴非有限值的指定颜色，None表示忽略这些点


class HistogramStyleConfig(BaseModel):
    """直方图边缘图配置 (Plotly go.Histogram)."""
    opacity: float = 0.7
    histnorm: Optional[str] = None  # "density", "probability", "percent", None
    nbins: Optional[int] = None  # 箱数，None表示自动
    bargap: float = 0.1  # 柱间间隙
    marker_line_width: float = 0.5
    marker_line_color: Optional[str] = None


class BoxStyleConfig(BaseModel):
    """箱线图边缘图配置 (Plotly go.Box)."""
    opacity: float = 0.7
    line_width: float = 1.0
    boxpoints: str = "outliers"  # "all", "outliers", "suspectedoutliers", False
    boxmean: bool = False  # 显示均值
    fillcolor: Optional[str] = None  # 箱体填充色
    notched: bool = False  # 是否显示缺口
    whiskerwidth: float = 0.5  # 须线宽度


class ViolinStyleConfig(BaseModel):
    """小提琴图边缘图配置 (Plotly go.Violin)."""
    opacity: float = 0.7
    line_width: float = 1.0
    points: str = "outliers"  # "all", "outliers", "suspectedoutliers", False
    box_visible: bool = False  # 显示内部箱线图
    meanline_visible: bool = False  # 显示均值线
    side: str = "both"  # "both", "positive", "negative"
    scalemode: str = "width"  # "width", "count"
    bandwidth: Optional[float] = None  # KDE带宽


class KDEStyleConfig(BaseModel):
    """KDE边缘图配置 (基于Violin实现)."""
    opacity: float = 0.7
    line_width: float = 2.0
    fill_opacity: float = 0.3
    side: str = "positive"  # "both", "positive", "negative"
    bandwidth: Optional[float] = None  # KDE带宽，None表示自动
    smoothing: float = 1.0  # 平滑度因子


class MarginalConfig(BaseModel):
    """Configuration for marginal plots."""
    enabled: MarginalEnabled = MarginalEnabled.NONE
    kind: MarginalKind = MarginalKind.HIST
    series: Union[str, List[str]] = "all"
    bins: Union[int, List[float]] = 25
    overlay: str = "overlay"
    size_x: float = 0.15  # X轴边缘图高度占比 (0.1-0.3)
    size_y: float = 0.15  # Y轴边缘图宽度占比 (0.1-0.3)
    spacing: float = 0.01  # 边缘图与主图间距

    # Plotly 6.0高级配置 - 根据类型选择对应的样式配置
    hist_style: HistogramStyleConfig = Field(default_factory=HistogramStyleConfig)
    box_style: BoxStyleConfig = Field(default_factory=BoxStyleConfig)
    violin_style: ViolinStyleConfig = Field(default_factory=ViolinStyleConfig)
    kde_style: KDEStyleConfig = Field(default_factory=KDEStyleConfig)

    # 布局精确控制
    gap_x: float = 0.02  # X边缘图与主图的间隙
    gap_y: float = 0.02  # Y边缘图与主图的间隙

    # 颜色联动控制
    color_sync: bool = True  # 是否与主图颜色同步

    # 独立样式控制（当color_sync=False时使用）
    independent_colors: Optional[List[str]] = None

    # 交互控制
    linked_selection: bool = True  # 图例选择是否影响边缘图
    hover_sync: bool = True  # 悬停信息同步

    def get_current_style(self) -> Union[HistogramStyleConfig, BoxStyleConfig, ViolinStyleConfig, KDEStyleConfig]:
        """根据当前kind返回对应的样式配置."""
        if self.kind == MarginalKind.HIST:
            return self.hist_style
        elif self.kind == MarginalKind.BOX:
            return self.box_style
        elif self.kind == MarginalKind.VIOLIN:
            return self.violin_style
        elif self.kind == MarginalKind.KDE:
            return self.kde_style
        else:
            return self.hist_style  # 默认


class FigureConfig(BaseModel):
    """Configuration for figure-level settings."""
    title: str = "Cross-Plot"
    size: tuple = (800, 600)
    watermark: Optional[Dict[str, Any]] = None

    # 高级布局配置
    autosize: bool = True  # 自动调整大小
    margin: Dict[str, int] = Field(default_factory=lambda: {
        "l": 80, "r": 80, "t": 100, "b": 80, "pad": 0
    })

    # 悬停模式
    hovermode: str = "closest"  # "closest", "x", "y", "x unified", "y unified", False

    # 拖拽和缩放
    dragmode: str = "zoom"  # "zoom", "pan", "select", "lasso", "orbit", "turntable"

    # 工具栏
    showlegend: bool = True

    # 分隔符和网格
    separators: Optional[str] = None  # 数字分隔符，如 ".,", ".,"


class StyleConfig(BaseModel):
    """Global style configuration."""
    font: Dict[str, Any] = Field(default_factory=lambda: {
        "family": "Arial",
        "size_title": 14,
        "size_label": 12,
        "size_ticks": 10,
        "color": "#333333"
    })
    color: Dict[str, Any] = Field(default_factory=lambda: {
        "cycle": ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"],
        "background_canvas": "#ffffff",
        "background_plot": "#ffffff",
        "background_marginal": "#f7f7f7",
        "grid_major": "#bbbbbb",
        "grid_minor": "#dddddd",
        "tick_major": "#666666",
        "tick_minor": "#999999"
    })
    linewidth: Dict[str, float] = Field(default_factory=lambda: {
        "main": 1.2,
        "grid_major": 0.6,
        "grid_minor": 0.4,
        "reference_line": 1.0,
        "tick_major": 1.0,
        "tick_minor": 0.8
    })
    tick: Dict[str, float] = Field(default_factory=lambda: {
        "major_length": 6,
        "minor_length": 4
    })
    marker: Dict[str, Any] = Field(default_factory=lambda: {
        "default_size": 36,
        "default_alpha": 0.6,
        "default_symbol": "circle",
        "default_edgewidth": 0.5
    })
    frame: Dict[str, Any] = Field(default_factory=lambda: {
        "color": "#666666",
        "width": 0.8
    })


class ReferenceLineConfig(BaseModel):
    """Configuration for reference lines."""
    visible: bool = False
    slope: float = 1.0  # 斜率，默认1:1线
    intercept: float = 0.0  # 截距
    color: Optional[str] = None  # 颜色，None时使用默认
    width: Optional[float] = None  # 线宽，None时使用默认
    style: str = "dashdot"  # 线型：solid, dash, dot, dashdot
    name: str = "Reference Line"  # 图例名称
    show_legend: bool = False  # 是否在图例中显示


class CrossPlotConfig(BaseModel):
    """Main configuration object for cross-plot generation."""
    xaxis: AxisConfig = Field(default_factory=AxisConfig)
    yaxis: AxisConfig = Field(default_factory=AxisConfig)
    series: List[SeriesConfig] = Field(default_factory=list)
    colorbar: ColorbarConfig = Field(default_factory=ColorbarConfig)
    marginal: MarginalConfig = Field(default_factory=MarginalConfig)
    figure: FigureConfig = Field(default_factory=FigureConfig)
    legend: LegendConfig = Field(default_factory=LegendConfig)
    style: StyleConfig = Field(default_factory=StyleConfig)
    error_bars: ErrorBarConfig = Field(default_factory=ErrorBarConfig)
    reference_line: Union[bool, ReferenceLineConfig] = False  # 向后兼容

    @field_validator('series', mode='before')
    @classmethod
    def ensure_series_list(cls, v):
        """Ensure series is always a list."""
        if isinstance(v, dict):
            return [v]
        return v


# ==============================================================================
# Section: Component Registration System
# ==============================================================================

class MarginalDrawer(ABC):
    """Abstract base class for marginal plot drawers."""

    @abstractmethod
    def draw(self, fig: go.Figure, data: List[float], series_config: SeriesConfig,
             marginal_config: MarginalConfig, row: int, col: int,
             orientation: str = 'vertical', bins: Optional[np.ndarray] = None) -> None:
        """Draw a marginal plot on the specified subplot."""
        pass


class HistogramDrawer(MarginalDrawer):
    """Drawer for histogram marginal plots."""

    def draw(self, fig: go.Figure, data: List[float], series_config: SeriesConfig,
             marginal_config: MarginalConfig, row: int, col: int,
             orientation: str = 'vertical', bins: Optional[np.ndarray] = None) -> None:

        # 使用对应的样式配置
        style = marginal_config.hist_style

        if bins is not None:
            # Use pre-calculated logarithmic bins
            positive_data = [v for v in data if v > 0]
            if positive_data:
                counts, bin_edges = np.histogram(positive_data, bins=bins, density=True)
                bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
                bin_widths = np.diff(bin_edges)

                if orientation == 'vertical':
                    fig.add_trace(go.Bar(
                        x=bin_centers, y=counts, width=bin_widths,
                        name=series_config.id, legendgroup=series_config.id,
                        showlegend=False,
                        marker_color=self._get_color(series_config),
                        opacity=style.opacity,
                        marker_line=dict(width=style.marker_line_width, color=style.marker_line_color)
                    ), row=row, col=col)
                else:  # horizontal
                    fig.add_trace(go.Bar(
                        y=bin_centers, x=counts, width=bin_widths, orientation='h',
                        name=series_config.id, legendgroup=series_config.id,
                        showlegend=False,
                        marker_color=self._get_color(series_config),
                        opacity=style.opacity,
                        marker_line=dict(width=style.marker_line_width, color=style.marker_line_color)
                    ), row=row, col=col)
        else:
            # Use standard histogram
            nbins = style.nbins or (marginal_config.bins if isinstance(marginal_config.bins, int) else 25)

            if orientation == 'vertical':
                fig.add_trace(go.Histogram(
                    x=data,
                    name=series_config.id,
                    legendgroup=series_config.id,
                    showlegend=False,
                    marker_color=self._get_color(series_config),
                    nbinsx=nbins,
                    histnorm=style.histnorm,
                    opacity=style.opacity,
                    marker_line=dict(width=style.marker_line_width, color=style.marker_line_color)
                ), row=row, col=col)
            else:  # horizontal
                fig.add_trace(go.Histogram(
                    y=data,
                    name=series_config.id,
                    legendgroup=series_config.id,
                    showlegend=False,
                    marker_color=self._get_color(series_config),
                    nbinsy=nbins,
                    histnorm=style.histnorm,
                    opacity=style.opacity,
                    marker_line=dict(width=style.marker_line_width, color=style.marker_line_color)
                ), row=row, col=col)

    def _get_color(self, series_config: SeriesConfig) -> str:
        """Get the color for this series."""
        return series_config.marker.facecolor or "#1f77b4"


class BoxDrawer(MarginalDrawer):
    """Drawer for box plot marginal plots."""

    def draw(self, fig: go.Figure, data: List[float], series_config: SeriesConfig,
             marginal_config: MarginalConfig, row: int, col: int,
             orientation: str = 'vertical', bins: Optional[np.ndarray] = None) -> None:

        # 使用对应的样式配置
        style = marginal_config.box_style

        if orientation == 'vertical':
            fig.add_trace(go.Box(
                x=data,
                name=series_config.id,
                legendgroup=series_config.id,
                showlegend=False,
                marker_color=self._get_color(series_config),
                # 高级配置
                boxpoints=style.boxpoints,
                boxmean=style.boxmean,
                fillcolor=style.fillcolor or self._get_color(series_config),
                opacity=style.opacity,
                line=dict(width=style.line_width),
                notched=style.notched,
                whiskerwidth=style.whiskerwidth
            ), row=row, col=col)
        else:  # horizontal
            fig.add_trace(go.Box(
                y=data,
                name=series_config.id,
                legendgroup=series_config.id,
                showlegend=False,
                marker_color=self._get_color(series_config),
                # 高级配置
                boxpoints=style.boxpoints,
                boxmean=style.boxmean,
                fillcolor=style.fillcolor or self._get_color(series_config),
                opacity=style.opacity,
                line=dict(width=style.line_width),
                notched=style.notched,
                whiskerwidth=style.whiskerwidth
            ), row=row, col=col)

    def _get_color(self, series_config: SeriesConfig) -> str:
        """Get the color for this series."""
        return series_config.marker.facecolor or "#1f77b4"

    def _get_fill_color(self, series_config: SeriesConfig) -> str:
        """Get the fill color with transparency for this series."""
        color = self._get_color(series_config)
        # Convert to rgba format with transparency
        if color.startswith('#'):
            # Convert hex to rgb with alpha
            hex_color = color.lstrip('#')
            if len(hex_color) == 6:
                r = int(hex_color[0:2], 16)
                g = int(hex_color[2:4], 16)
                b = int(hex_color[4:6], 16)
                return f'rgba({r},{g},{b},0.3)'
        return color


# Component Registry
MARGINAL_DRAWERS: Dict[MarginalKind, MarginalDrawer] = {
    MarginalKind.HIST: HistogramDrawer(),
    MarginalKind.BOX: BoxDrawer(),
    # Additional drawers will be registered here
}


def register_marginal_drawer(kind: MarginalKind, drawer: MarginalDrawer) -> None:
    """Register a new marginal plot drawer."""
    MARGINAL_DRAWERS[kind] = drawer


# ==============================================================================
# Section: Data Point Model
# ==============================================================================

@dataclass
class DataPoint:
    """Represents a single data point in the cross-plot."""
    x: float
    y: float
    z: Optional[float] = None
    error_x: Optional[float] = None
    error_y: Optional[float] = None
    series_id: str = "default"


# ==============================================================================
# Section: Main CrossPlotter Class
# ==============================================================================

class CrossPlotter:
    """Main class for creating cross-plots with marginal distributions."""

    def __init__(self, config: Union[CrossPlotConfig, Dict[str, Any]]):
        """Initialize the cross-plotter with configuration."""
        if isinstance(config, dict):
            self.config = CrossPlotConfig(**config)
        else:
            self.config = config

    def create_plot(self, data: List[DataPoint]) -> go.Figure:
        """Create a cross-plot figure from the provided data."""
        # Group data by series
        data_by_series = self._group_data_by_series(data)

        # Create subplot layout
        fig = self._create_subplot_layout()

        # Pre-calculate logarithmic bins if needed
        log_bins_x, log_bins_y = self._calculate_logarithmic_bins(data)

        # Draw main traces and marginal plots
        self._draw_traces(fig, data_by_series, log_bins_x, log_bins_y)

        # Apply layout and styling
        self._apply_layout_and_styling(fig)

        # Add reference line if requested
        if self.config.reference_line:
            self._add_reference_line(fig, data)

        return fig

    def _group_data_by_series(self, data: List[DataPoint]) -> Dict[str, List[DataPoint]]:
        """Group data points by series ID."""
        data_by_series = {}
        for point in data:
            if point.series_id not in data_by_series:
                data_by_series[point.series_id] = []
            data_by_series[point.series_id].append(point)
        return data_by_series

    def _clean_z_values(self, z_vals: List[float], colorbar_config: ColorbarConfig) -> List[float]:
        """
        Clean Z values by handling non-finite values according to R-12 specification.

        Args:
            z_vals: List of Z values (may contain NaN, inf, -inf)
            colorbar_config: Colorbar configuration containing nan_color setting

        Returns:
            List of cleaned Z values

        Raises:
            Warning: If non-finite values are found and processed
        """
        if not z_vals:
            return z_vals

        # Convert to numpy array for efficient processing
        z_array = np.array(z_vals)

        # Check for non-finite values (NaN, inf, -inf)
        non_finite_mask = ~np.isfinite(z_array)

        if not np.any(non_finite_mask):
            # No non-finite values found, return original list
            return z_vals

        # Count non-finite values for warning
        num_non_finite = np.sum(non_finite_mask)

        if colorbar_config.nan_color is not None:
            # Replace non-finite values with a special numeric value
            # We'll use the minimum finite value minus a small offset
            finite_values = z_array[~non_finite_mask]
            if len(finite_values) > 0:
                # Use a value outside the normal range to represent NaN color
                nan_replacement = np.min(finite_values) - 1.0
                z_array[non_finite_mask] = nan_replacement
                print(f"Warning: Found {num_non_finite} non-finite Z values, "
                      f"replaced with special value for nan_color '{colorbar_config.nan_color}'")
            else:
                # All values are non-finite, use a default value
                z_array[non_finite_mask] = 0.0
                print(f"Warning: All {num_non_finite} Z values are non-finite, "
                      f"using default value for nan_color '{colorbar_config.nan_color}'")
        else:
            # Remove non-finite values by setting them to None (will be filtered later)
            print(f"Warning: Found {num_non_finite} non-finite Z values, ignoring these points")
            # Return None for non-finite values - they will be handled in the calling function
            return [val if np.isfinite(val) else None for val in z_vals]

        return z_array.tolist()

    def _create_subplot_layout(self) -> go.Figure:
        """Create the subplot layout based on marginal configuration."""
        has_marginal_x = self.config.marginal.enabled in [MarginalEnabled.X, MarginalEnabled.BOTH]
        has_marginal_y = self.config.marginal.enabled in [MarginalEnabled.Y, MarginalEnabled.BOTH]

        # Get configurable sizes
        marginal_x_size = self.config.marginal.size_x
        marginal_y_size = self.config.marginal.size_y
        spacing = self.config.marginal.spacing

        # Calculate main plot sizes
        main_width = 1.0 - marginal_y_size if has_marginal_y else 1.0
        main_height = 1.0 - marginal_x_size if has_marginal_x else 1.0

        if has_marginal_x and has_marginal_y:
            # 2x2 grid: marginal X (top), marginal Y (right), main (bottom-left)
            fig = make_subplots(
                rows=2, cols=2,
                column_widths=[main_width, marginal_y_size],
                row_heights=[marginal_x_size, main_height],
                specs=[[{"type": "xy"}, {"type": "xy"}],
                       [{"type": "scatter"}, {"type": "xy"}]],
                horizontal_spacing=spacing, vertical_spacing=spacing,
                shared_xaxes='columns', shared_yaxes='rows'
            )
        elif has_marginal_x:
            # 2x1 grid: marginal X (top), main (bottom)
            fig = make_subplots(
                rows=2, cols=1, row_heights=[marginal_x_size, main_height],
                specs=[[{"type": "xy"}], [{"type": "scatter"}]],
                vertical_spacing=spacing, shared_xaxes=True
            )
        elif has_marginal_y:
            # 1x2 grid: main (left), marginal Y (right)
            fig = make_subplots(
                rows=1, cols=2, column_widths=[main_width, marginal_y_size],
                specs=[[{"type": "scatter"}, {"type": "xy"}]],
                horizontal_spacing=spacing, shared_yaxes=True
            )
        else:
            # 1x1 grid: main plot only
            fig = make_subplots(rows=1, cols=1, specs=[[{"type": "scatter"}]])

        return fig

    def _calculate_logarithmic_bins(self, data: List[DataPoint]) -> tuple:
        """Calculate logarithmic bins for marginal plots if needed."""
        has_marginal_x = self.config.marginal.enabled in [MarginalEnabled.X, MarginalEnabled.BOTH]
        has_marginal_y = self.config.marginal.enabled in [MarginalEnabled.Y, MarginalEnabled.BOTH]

        log_bins_x = None
        if (has_marginal_x and
            self.config.xaxis.scale == ScaleType.LOG and
            self.config.marginal.kind == MarginalKind.HIST):

            all_x_vals = [d.x for d in data if d.x > 0]
            if all_x_vals:
                num_bins = self.config.marginal.bins if isinstance(self.config.marginal.bins, int) else 25
                min_val = np.log10(min(all_x_vals))
                max_val = np.log10(max(all_x_vals))
                log_bins_x = np.logspace(min_val, max_val, num_bins)

        log_bins_y = None
        if (has_marginal_y and
            self.config.yaxis.scale == ScaleType.LOG and
            self.config.marginal.kind == MarginalKind.HIST):

            all_y_vals = [d.y for d in data if d.y > 0]
            if all_y_vals:
                num_bins = self.config.marginal.bins if isinstance(self.config.marginal.bins, int) else 25
                min_val = np.log10(min(all_y_vals))
                max_val = np.log10(max(all_y_vals))
                log_bins_y = np.logspace(min_val, max_val, num_bins)

        return log_bins_x, log_bins_y

    def _draw_traces(self, fig: go.Figure, data_by_series: Dict[str, List[DataPoint]],
                     log_bins_x: Optional[np.ndarray], log_bins_y: Optional[np.ndarray]) -> None:
        """Draw all traces (main and marginal) on the figure."""
        # Get color cycle
        color_cycle = itertools.cycle(self.config.style.color["cycle"])

        # Determine subplot positions
        has_marginal_x = self.config.marginal.enabled in [MarginalEnabled.X, MarginalEnabled.BOTH]
        has_marginal_y = self.config.marginal.enabled in [MarginalEnabled.Y, MarginalEnabled.BOTH]

        main_row, main_col = self._get_main_subplot_position(has_marginal_x, has_marginal_y)

        # Track colorbar display (only for global colorbar)
        global_colorbar_shown = False

        for series_id, series_data in data_by_series.items():
            # Get or create series configuration
            series_config = self._get_series_config(series_id, color_cycle)

            # Extract data arrays
            x_vals = [d.x for d in series_data]
            y_vals = [d.y for d in series_data]
            z_vals = [d.z for d in series_data if d.z is not None]
            error_x_vals = [d.error_x for d in series_data if d.error_x is not None]
            error_y_vals = [d.error_y for d in series_data if d.error_y is not None]

            # 判断是否显示全局颜色轴（仅当没有系列独立颜色轴时）
            has_series_colorbar = series_config.colorbar and series_config.colorbar.visible and len(z_vals) > 0
            show_global_colorbar = (not global_colorbar_shown and
                                  not has_series_colorbar and
                                  self.config.colorbar.visible and len(z_vals) > 0)

            # Draw main trace
            self._draw_main_trace(fig, series_config, x_vals, y_vals, z_vals,
                                error_x_vals, error_y_vals, main_row, main_col,
                                show_global_colorbar)

            if show_global_colorbar:
                global_colorbar_shown = True

            # Draw marginal traces
            self._draw_marginal_traces(fig, series_config, x_vals, y_vals,
                                     has_marginal_x, has_marginal_y,
                                     log_bins_x, log_bins_y, main_col, main_row)

    def _get_main_subplot_position(self, has_marginal_x: bool, has_marginal_y: bool) -> tuple:
        """Get the row and column position of the main subplot."""
        if has_marginal_x and has_marginal_y:
            return (2, 1)  # Bottom-left in 2x2 grid
        elif has_marginal_x:
            return (2, 1)  # Bottom in 2x1 grid
        elif has_marginal_y:
            return (1, 1)  # Left in 1x2 grid
        else:
            return (1, 1)  # Only subplot in 1x1 grid

    def _get_series_config(self, series_id: str, color_cycle: itertools.cycle) -> SeriesConfig:
        """Get or create series configuration with color assignment."""
        # Look for existing series configuration
        for series_config in self.config.series:
            if series_config.id == series_id:
                # Apply color from cycle if not explicitly set
                if series_config.marker.facecolor is None:
                    series_config.marker.facecolor = next(color_cycle)
                return series_config

        # Create new series configuration with default values
        default_color = next(color_cycle)
        return SeriesConfig(
            id=series_id,
            marker=MarkerConfig(facecolor=default_color),
            line=LineConfig(color=default_color)
        )

    def _draw_main_trace(self, fig: go.Figure, series_config: SeriesConfig,
                        x_vals: List[float], y_vals: List[float], z_vals: List[float],
                        error_x_vals: List[float], error_y_vals: List[float],
                        row: int, col: int, show_colorbar: bool) -> None:
        """Draw the main scatter/line trace."""
        # Determine trace mode based on series type
        mode = self._get_trace_mode(series_config.type)

        # Build trace arguments
        trace_args = {
            'x': x_vals,
            'y': y_vals,
            'mode': mode,
            'name': series_config.id,
            'legendgroup': series_config.id,
            'marker': {
                'symbol': series_config.marker.symbol,
                'size': series_config.marker.size,
                'opacity': series_config.alpha,
                'line': {
                    'color': series_config.marker.linecolor,
                    'width': series_config.marker.edgewidth
                }
            },
            'line': {
                'color': series_config.line.color or series_config.marker.facecolor,
                'width': series_config.line.width,
                'dash': self._convert_line_style(series_config.line.style)
            }
        }

        # Handle color mapping (highest priority)
        # 优先检查系列独立的颜色轴配置
        series_colorbar = series_config.colorbar
        use_series_colorbar = series_colorbar and series_colorbar.visible and z_vals
        use_global_colorbar = self.config.colorbar.visible and z_vals and not use_series_colorbar

        if use_series_colorbar:
            # 使用系列独立的颜色轴配置
            trace_args['marker']['color'] = z_vals
            trace_args['marker']['colorscale'] = series_config.colorscale or series_colorbar.cmap
            trace_args['marker']['showscale'] = True  # 每个系列都显示自己的颜色轴

            # Convert orientation to Plotly format
            orientation = 'v' if series_colorbar.orientation == 'vertical' else 'h'
            colorbar_config = {
                'title': series_colorbar.title,
                'orientation': orientation,
                'thickness': series_colorbar.thickness,
                'len': series_colorbar.len
            }

            # 设置颜色轴位置
            if orientation == 'v':
                if series_colorbar.x is not None:
                    colorbar_config['x'] = series_colorbar.x
                if series_colorbar.y is not None:
                    colorbar_config['y'] = series_colorbar.y
                if series_colorbar.yanchor:
                    colorbar_config['yanchor'] = series_colorbar.yanchor
                if series_colorbar.xanchor:
                    colorbar_config['xanchor'] = series_colorbar.xanchor
            else:
                if series_colorbar.x is not None:
                    colorbar_config['x'] = series_colorbar.x
                if series_colorbar.y is not None:
                    colorbar_config['y'] = series_colorbar.y
                if series_colorbar.xanchor:
                    colorbar_config['xanchor'] = series_colorbar.xanchor
                if series_colorbar.yanchor:
                    colorbar_config['yanchor'] = series_colorbar.yanchor

            trace_args['marker']['colorbar'] = colorbar_config

            if series_colorbar.clim:
                trace_args['marker']['cmin'] = series_colorbar.clim[0]
                trace_args['marker']['cmax'] = series_colorbar.clim[1]

        elif use_global_colorbar:
            # 使用全局颜色轴配置
            trace_args['marker']['color'] = z_vals
            trace_args['marker']['colorscale'] = self.config.colorbar.cmap
            trace_args['marker']['showscale'] = show_colorbar
            if show_colorbar:
                # Convert orientation to Plotly format
                orientation = 'v' if self.config.colorbar.orientation == 'vertical' else 'h'
                colorbar_config = {
                    'title': self.config.colorbar.title,
                    'orientation': orientation
                }

                # 使用Plotly的智能布局，只在必要时手动调整
                if orientation == 'v':
                    # 垂直颜色轴：使用默认位置，只调整厚度
                    colorbar_config.update({
                        'thickness': 15,  # 使其更薄
                        'len': 0.8  # 稍短一些
                    })
                else:
                    # 水平颜色轴：使用默认位置
                    colorbar_config.update({
                        'thickness': 15,
                        'len': 0.8
                    })

                trace_args['marker']['colorbar'] = colorbar_config

                if self.config.colorbar.clim:
                    trace_args['marker']['cmin'] = self.config.colorbar.clim[0]
                    trace_args['marker']['cmax'] = self.config.colorbar.clim[1]
        else:
            # Use series-specific color or default
            trace_args['marker']['color'] = series_config.marker.facecolor

        # Add error bars if configured and data available
        if (self.config.error_bars.visible or series_config.error_bars.visible) and error_x_vals:
            trace_args['error_x'] = {
                'type': 'data',
                'array': error_x_vals,
                'visible': True,
                'color': series_config.error_bars.color or series_config.marker.facecolor,
                'thickness': series_config.error_bars.thickness
            }

        if (self.config.error_bars.visible or series_config.error_bars.visible) and error_y_vals:
            trace_args['error_y'] = {
                'type': 'data',
                'array': error_y_vals,
                'visible': True,
                'color': series_config.error_bars.color or series_config.marker.facecolor,
                'thickness': series_config.error_bars.thickness
            }

        # showscale should now always be a boolean due to proper logic above

        # Add trace to figure
        fig.add_trace(go.Scatter(**trace_args), row=row, col=col)

    def _get_trace_mode(self, series_type: SeriesType) -> str:
        """Convert series type to Plotly trace mode."""
        if series_type == SeriesType.SCATTER:
            return 'markers'
        elif series_type == SeriesType.LINE:
            return 'lines'
        elif series_type == SeriesType.BOTH:
            return 'markers+lines'
        else:
            return 'markers'

    def _convert_line_style(self, style: str) -> str:
        """Convert line style to Plotly dash format."""
        style_map = {
            'solid': 'solid',
            'dash': 'dash',
            'dot': 'dot',
            'dashdot': 'dashdot'
        }
        return style_map.get(style, 'solid')

    def _draw_marginal_traces(self, fig: go.Figure, series_config: SeriesConfig,
                            x_vals: List[float], y_vals: List[float],
                            has_marginal_x: bool, has_marginal_y: bool,
                            log_bins_x: Optional[np.ndarray], log_bins_y: Optional[np.ndarray],
                            main_col: int, main_row: int) -> None:
        """Draw marginal traces for the current series."""
        drawer = MARGINAL_DRAWERS.get(self.config.marginal.kind)
        if not drawer:
            print(f"Warning: No drawer found for marginal kind '{self.config.marginal.kind}'")
            return

        # Draw X marginal (top)
        if has_marginal_x:
            drawer.draw(
                fig=fig,
                data=x_vals,
                series_config=series_config,
                marginal_config=self.config.marginal,
                row=1,
                col=main_col,
                orientation='vertical',
                bins=log_bins_x
            )

        # Draw Y marginal (right)
        if has_marginal_y:
            y_col = 2 if has_marginal_x else 2  # Always column 2 for Y marginal
            drawer.draw(
                fig=fig,
                data=y_vals,
                series_config=series_config,
                marginal_config=self.config.marginal,
                row=main_row,
                col=y_col,
                orientation='horizontal',
                bins=log_bins_y
            )

    def _apply_layout_and_styling(self, fig: go.Figure) -> None:
        """Apply layout configuration and styling to the figure."""
        # Determine main axis names based on subplot layout
        has_marginal_x = self.config.marginal.enabled in [MarginalEnabled.X, MarginalEnabled.BOTH]
        has_marginal_y = self.config.marginal.enabled in [MarginalEnabled.Y, MarginalEnabled.BOTH]

        main_xaxis_name, main_yaxis_name = self._get_main_axis_names(has_marginal_x, has_marginal_y)

        # Build layout updates
        layout_updates = {
            'title': {
                'text': self.config.figure.title,
                'font': {
                    'family': self.config.style.font["family"],
                    'size': self.config.style.font["size_title"],
                    'color': self.config.style.font["color"]
                }
            },
            'plot_bgcolor': self.config.style.color["background_plot"],
            'paper_bgcolor': self.config.style.color["background_canvas"],
            'font': {
                'family': self.config.style.font["family"],
                'color': self.config.style.font["color"]
            },
            'legend': self._get_legend_config(),
            'width': self.config.figure.size[0],
            'height': self.config.figure.size[1]
        }

        # Configure main axes
        layout_updates[main_xaxis_name] = self._build_axis_config(self.config.xaxis, 'x')
        layout_updates[main_yaxis_name] = self._build_axis_config(self.config.yaxis, 'y')

        # Apply layout updates
        fig.update_layout(**layout_updates)

        # Apply global axis styling
        self._apply_global_axis_styling(fig)

        # Ensure marginal axes match main axes for logarithmic scales
        self._sync_marginal_axes(fig, has_marginal_x, has_marginal_y)

        # Set barmode for histograms
        if self.config.marginal.kind == MarginalKind.HIST:
            fig.update_layout(barmode='overlay' if self.config.marginal.overlay == 'overlay' else 'group')

    def _get_main_axis_names(self, has_marginal_x: bool, has_marginal_y: bool) -> tuple:
        """Get the axis names for the main subplot."""
        if has_marginal_x and has_marginal_y:
            return 'xaxis3', 'yaxis3'  # Bottom-left in 2x2 grid
        elif has_marginal_x:
            return 'xaxis2', 'yaxis2'  # Bottom in 2x1 grid
        else:
            return 'xaxis', 'yaxis'    # Main axes in 1x2 or 1x1 grid

    def _build_axis_config(self, axis_config: AxisConfig, axis_type: str) -> Dict[str, Any]:
        """Build axis configuration dictionary with advanced tick control."""
        # Convert scale type to Plotly-compatible format
        scale_type = self._convert_scale_type(axis_config.scale)

        config = {
            'title': {
                'text': axis_config.title.text,
                'font': {
                    'size': self.config.style.font["size_label"],
                    'color': self.config.style.font["color"]
                }
            },
            'type': scale_type,
            'autorange': 'reversed' if axis_config.inverted else True,
            'rangemode': axis_config.range.mode,
            'tickfont': {'size': self.config.style.font["size_ticks"]},

            # 主网格线配置（样式从StyleConfig获取）
            'showgrid': axis_config.grid.major_enabled,
            'gridcolor': self.config.style.color["grid_major"],
            'gridwidth': self.config.style.linewidth["grid_major"],
            'griddash': axis_config.grid.major_dash,

            # 主刻度配置（样式从StyleConfig获取）
            'ticks': axis_config.ticks.position,
            'ticklen': self.config.style.tick["major_length"],
            'tickwidth': self.config.style.linewidth["tick_major"],
            'tickcolor': self.config.style.color["tick_major"],
        }

        # 刻度模式配置
        if axis_config.ticks.mode == "linear" and axis_config.ticks.interval is not None:
            config['tickmode'] = 'linear'
            config['dtick'] = axis_config.ticks.interval
            if axis_config.ticks.start is not None:
                config['tick0'] = axis_config.ticks.start

        # 次要刻度配置（样式从StyleConfig获取）
        if axis_config.ticks.minor_enabled:
            minor_config = {
                'ticks': axis_config.ticks.minor_position,
                'ticklen': self.config.style.tick["minor_length"],
                'tickcolor': self.config.style.color["tick_minor"],
                'showgrid': axis_config.grid.minor_enabled,
            }

            if axis_config.grid.minor_enabled:
                minor_config.update({
                    'gridcolor': self.config.style.color["grid_minor"],
                    'gridwidth': self.config.style.linewidth["grid_minor"],
                    'griddash': axis_config.grid.minor_dash,
                })

            if axis_config.ticks.minor_interval is not None:
                minor_config['dtick'] = axis_config.ticks.minor_interval
                if axis_config.ticks.minor_start is not None:
                    minor_config['tick0'] = axis_config.ticks.minor_start

            config['minor'] = minor_config

        # 手动范围设置
        if axis_config.range.min_value is not None or axis_config.range.max_value is not None:
            range_values = []
            if axis_config.range.min_value is not None:
                range_values.append(axis_config.range.min_value)
            if axis_config.range.max_value is not None:
                if len(range_values) == 0:
                    range_values = [None, axis_config.range.max_value]
                else:
                    range_values.append(axis_config.range.max_value)
            if len(range_values) == 2:
                config['range'] = range_values
                config['autorange'] = False

        # 刻度标签配置
        if not axis_config.show_tick_labels:
            config['showticklabels'] = False
        if axis_config.tick_angle is not None:
            config['tickangle'] = axis_config.tick_angle
        if axis_config.tick_prefix:
            config['tickprefix'] = axis_config.tick_prefix
        if axis_config.tick_suffix:
            config['ticksuffix'] = axis_config.tick_suffix
        if axis_config.tick_format:
            config['tickformat'] = axis_config.tick_format

        return config

    def _get_legend_config(self) -> Dict[str, Any]:
        """Get legend configuration, adjusting position based on colorbar presence."""
        legend_config = {
            'font': {'size': self.config.style.font["size_ticks"]},
            'bgcolor': 'rgba(255,255,255,0.8)'
        }

        # Adjust legend position based on colorbar configuration
        if self.config.colorbar.visible:
            if self.config.colorbar.orientation == 'vertical':
                # Colorbar is vertical (right side), move legend to top-left inside plot
                legend_config.update({
                    'x': 0.02,
                    'y': 0.98,
                    'xanchor': 'left',
                    'yanchor': 'top',
                    'bordercolor': '#cccccc',
                    'borderwidth': 1
                })
            else:
                # Colorbar is horizontal (bottom), place legend on right side
                legend_config.update({
                    'x': 1.02,
                    'y': 1,
                    'xanchor': 'left',
                    'yanchor': 'top'
                })
        else:
            # No colorbar, use default right position
            legend_config.update({
                'x': 1.02,
                'y': 1,
                'xanchor': 'left',
                'yanchor': 'top'
            })

        return legend_config

    def _convert_scale_type(self, scale_type: ScaleType) -> str:
        """Convert ScaleType enum to Plotly-compatible string."""
        # 直接返回枚举值，因为现在枚举值就是Plotly兼容的
        return scale_type.value

    def _apply_global_axis_styling(self, fig: go.Figure) -> None:
        """Apply global styling to all axes."""
        # Apply default styling to all axes
        tick_color = self.config.style.frame["color"]
        tick_width = self.config.style.frame["width"]

        fig.update_xaxes(
            showline=True,
            linewidth=tick_width,
            linecolor=tick_color,
            ticks='outside',
            tickcolor=tick_color,
            tickwidth=tick_width,
            ticklen=6,  # Standard tick length for main plot
            mirror=True
        )
        fig.update_yaxes(
            showline=True,
            linewidth=tick_width,
            linecolor=tick_color,
            ticks='outside',
            tickcolor=tick_color,
            tickwidth=tick_width,
            ticklen=6,
            mirror=True
        )

        # Apply specific styling to marginal plot axes
        # 边缘图设计原则：
        # - 数据轴（与主图共享）：不显示刻度线
        # - 计数轴（独立的频率/密度轴）：显示刻度线，位置在外侧
        has_marginal_x = self.config.marginal.enabled in [MarginalEnabled.X, MarginalEnabled.BOTH]
        has_marginal_y = self.config.marginal.enabled in [MarginalEnabled.Y, MarginalEnabled.BOTH]

        # Get tick styling from main plot configuration
        tick_color = self.config.style.frame["color"]
        tick_width = self.config.style.frame["width"]

        if has_marginal_x:
            # X marginal plot is always at row=1
            # X轴（数据轴，与主图共享）：不显示刻度线
            fig.update_xaxes(
                ticks='',  # 不显示刻度线
                row=1
            )
            # Y轴（计数轴）：显示刻度线，在外侧
            fig.update_yaxes(
                ticks='outside',
                tickcolor=tick_color,
                tickwidth=tick_width,
                ticklen=4,
                row=1
            )

        if has_marginal_y:
            # Y marginal plot position depends on layout
            if has_marginal_x:
                # 2x2 layout: Y marginal at row=2, col=2
                # X轴（计数轴）：显示刻度线，在外侧
                fig.update_xaxes(
                    ticks='outside',
                    tickcolor=tick_color,
                    tickwidth=tick_width,
                    ticklen=4,
                    row=2, col=2
                )
                # Y轴（数据轴，与主图共享）：不显示刻度线
                fig.update_yaxes(
                    ticks='',  # 不显示刻度线
                    row=2, col=2
                )
            else:
                # 1x2 layout: Y marginal at row=1, col=2
                # X轴（计数轴）：显示刻度线，在外侧
                fig.update_xaxes(
                    ticks='outside',
                    tickcolor=tick_color,
                    tickwidth=tick_width,
                    ticklen=4,
                    row=1, col=2
                )
                # Y轴（数据轴，与主图共享）：不显示刻度线
                fig.update_yaxes(
                    ticks='',  # 不显示刻度线
                    row=1, col=2
                )



    def _sync_marginal_axes(self, fig: go.Figure, has_marginal_x: bool, has_marginal_y: bool) -> None:
        """Ensure marginal axes match main axes for logarithmic scales."""
        if has_marginal_x and self.config.xaxis.scale == ScaleType.LOG:
            scale_type = self._convert_scale_type(self.config.xaxis.scale)
            fig.update_xaxes(type=scale_type, row=1, col=1)

        if has_marginal_y and self.config.yaxis.scale == ScaleType.LOG:
            y_col = 2
            y_row = 2 if has_marginal_x else 1
            scale_type = self._convert_scale_type(self.config.yaxis.scale)
            fig.update_yaxes(type=scale_type, row=y_row, col=y_col)

    def _add_reference_line(self, fig: go.Figure, data: List[DataPoint]) -> None:
        """Add reference line to the main plot based on slope and intercept."""
        # Parse reference line configuration
        ref_config = self._get_reference_line_config()
        if not ref_config.visible:
            return

        has_marginal_x = self.config.marginal.enabled in [MarginalEnabled.X, MarginalEnabled.BOTH]
        has_marginal_y = self.config.marginal.enabled in [MarginalEnabled.Y, MarginalEnabled.BOTH]
        main_row, main_col = self._get_main_subplot_position(has_marginal_x, has_marginal_y)

        # Calculate data range for reference line
        all_x = [d.x for d in data]
        all_y = [d.y for d in data]

        if all_x and all_y:
            # Get X axis range
            x_min, x_max = min(all_x), max(all_x)
            x_range = x_max - x_min
            x_min -= x_range * 0.05  # Extend slightly
            x_max += x_range * 0.05

            # Calculate Y values using y = slope * x + intercept
            y_start = ref_config.slope * x_min + ref_config.intercept
            y_end = ref_config.slope * x_max + ref_config.intercept

            # Determine line color and width
            line_color = ref_config.color or self.config.style.frame["color"]
            line_width = ref_config.width or self.config.style.linewidth["reference_line"]

            fig.add_trace(go.Scatter(
                x=[x_min, x_max],
                y=[y_start, y_end],
                mode='lines',
                line=dict(
                    color=line_color,
                    width=line_width,
                    dash=ref_config.style
                ),
                name=ref_config.name,
                showlegend=ref_config.show_legend,
                hoverinfo='skip'
            ), row=main_row, col=main_col)

    def _get_reference_line_config(self) -> ReferenceLineConfig:
        """Get reference line configuration, handling backward compatibility."""
        if isinstance(self.config.reference_line, bool):
            # Backward compatibility: convert bool to ReferenceLineConfig
            return ReferenceLineConfig(visible=self.config.reference_line)
        elif isinstance(self.config.reference_line, ReferenceLineConfig):
            return self.config.reference_line
        else:
            return ReferenceLineConfig(visible=False)


# ==============================================================================
# Section: Additional Marginal Drawers
# ==============================================================================

class ViolinDrawer(MarginalDrawer):
    """Drawer for violin plot marginal plots."""

    def draw(self, fig: go.Figure, data: List[float], series_config: SeriesConfig,
             marginal_config: MarginalConfig, row: int, col: int,
             orientation: str = 'vertical', bins: Optional[np.ndarray] = None) -> None:

        # 使用对应的样式配置
        style = marginal_config.violin_style

        if orientation == 'vertical':
            fig.add_trace(go.Violin(
                x=data,
                name=series_config.id,
                legendgroup=series_config.id,
                showlegend=False,
                line_color=self._get_color(series_config),
                fillcolor=self._get_fill_color(series_config),
                # 高级配置
                points=style.points,
                box_visible=style.box_visible,
                meanline_visible=style.meanline_visible,
                side=style.side,
                opacity=style.opacity,
                line=dict(width=style.line_width),
                scalemode=style.scalemode
            ), row=row, col=col)
        else:  # horizontal
            fig.add_trace(go.Violin(
                y=data,
                name=series_config.id,
                legendgroup=series_config.id,
                showlegend=False,
                line_color=self._get_color(series_config),
                fillcolor=self._get_fill_color(series_config),
                # 高级配置
                points=style.points,
                box_visible=style.box_visible,
                meanline_visible=style.meanline_visible,
                side=style.side,
                opacity=style.opacity,
                line=dict(width=style.line_width),
                scalemode=style.scalemode
            ), row=row, col=col)

    def _get_color(self, series_config: SeriesConfig) -> str:
        """Get the color for this series."""
        return series_config.marker.facecolor or "#1f77b4"

    def _get_fill_color(self, series_config: SeriesConfig) -> str:
        """Get the fill color with transparency for this series."""
        color = self._get_color(series_config)
        # Convert to rgba format with transparency
        if color.startswith('#'):
            # Convert hex to rgb with alpha
            hex_color = color.lstrip('#')
            if len(hex_color) == 6:
                r = int(hex_color[0:2], 16)
                g = int(hex_color[2:4], 16)
                b = int(hex_color[4:6], 16)
                return f'rgba({r},{g},{b},0.3)'
        return color


class KDEDrawer(MarginalDrawer):
    """Drawer for KDE (Kernel Density Estimation) marginal plots using Plotly's Violin."""

    def draw(self, fig: go.Figure, data: List[float], series_config: SeriesConfig,
             marginal_config: MarginalConfig, row: int, col: int,
             orientation: str = 'vertical', bins: Optional[np.ndarray] = None) -> None:

        # 使用Plotly内置的Violin图来实现KDE
        # Violin图本质上就是KDE的可视化
        if len(data) < 2:
            return

        # 使用对应的样式配置
        style = marginal_config.kde_style

        # 使用Violin图实现KDE效果
        if orientation == 'vertical':
            fig.add_trace(go.Violin(
                x=data,
                orientation='h',  # 水平方向的violin用于垂直边际图
                side=style.side,  # 使用配置的侧面显示
                width=2.0,  # 控制宽度
                name=series_config.id,
                legendgroup=series_config.id,
                showlegend=False,
                line_color=self._get_color(series_config),
                fillcolor=self._get_fill_color(series_config),
                meanline_visible=False,  # KDE通常不显示均值线
                box_visible=False,  # KDE不显示箱线图
                points=False,  # KDE不显示数据点
                scalemode='width',  # 按宽度缩放
                opacity=style.opacity,
                line=dict(width=style.line_width)
            ), row=row, col=col)
        else:  # horizontal
            fig.add_trace(go.Violin(
                y=data,
                orientation='v',  # 垂直方向的violin用于水平边际图
                side=style.side,  # 使用配置的侧面显示
                width=2.0,  # 控制宽度
                name=series_config.id,
                legendgroup=series_config.id,
                showlegend=False,
                line_color=self._get_color(series_config),
                fillcolor=self._get_fill_color(series_config),
                meanline_visible=False,  # KDE通常不显示均值线
                box_visible=False,  # KDE不显示箱线图
                points=False,  # KDE不显示数据点
                scalemode='width',  # 按宽度缩放
                opacity=style.opacity,
                line=dict(width=style.line_width)
            ), row=row, col=col)

    def _get_color(self, series_config: SeriesConfig) -> str:
        """Get the color for this series."""
        return series_config.marker.facecolor or "#1f77b4"

    def _get_fill_color(self, series_config: SeriesConfig) -> str:
        """Get the fill color with transparency for this series."""
        color = self._get_color(series_config)
        # Convert to rgba format with transparency
        if color.startswith('#'):
            # Convert hex to rgb with alpha
            hex_color = color.lstrip('#')
            if len(hex_color) == 6:
                r = int(hex_color[0:2], 16)
                g = int(hex_color[2:4], 16)
                b = int(hex_color[4:6], 16)
                return f'rgba({r},{g},{b},0.3)'
        return color


# Register additional drawers
MARGINAL_DRAWERS[MarginalKind.VIOLIN] = ViolinDrawer()
MARGINAL_DRAWERS[MarginalKind.KDE] = KDEDrawer()


# ==============================================================================
# Section: Theme Configurations
# ==============================================================================

def get_default_theme() -> StyleConfig:
    """Get the default light theme configuration."""
    return StyleConfig()


def get_dark_theme() -> StyleConfig:
    """Get a dark theme configuration."""
    return StyleConfig(
        font={
            "family": "Arial",
            "size_title": 14,
            "size_label": 12,
            "size_ticks": 10,
            "color": "#ffffff"
        },
        color={
            "cycle": ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"],
            "background_canvas": "#2f2f2f",
            "background_plot": "#3f3f3f",
            "background_marginal": "#4f4f4f",
            "grid_major": "#666666",
            "grid_minor": "#555555",
            "tick_major": "#cccccc",
            "tick_minor": "#aaaaaa"
        },
        linewidth={
            "main": 1.2,
            "grid_major": 0.6,
            "grid_minor": 0.4,
            "reference_line": 1.0,
            "tick_major": 1.0,
            "tick_minor": 0.8
        },
        tick={
            "major_length": 6,
            "minor_length": 4
        },
        marker={
            "default_size": 36,
            "default_alpha": 0.7,
            "default_symbol": "circle",
            "default_edgewidth": 0.5
        },
        frame={
            "color": "#cccccc",
            "width": 0.8
        }
    )


# ==============================================================================
# Section: Data Generation Utilities
# ==============================================================================

def generate_sample_data(num_points_per_series: int = 150, seed: int = 42) -> List[DataPoint]:
    """
    Generate sample data for testing and demonstration.

    Args:
        num_points_per_series: Number of points to generate for each series
        seed: Random seed for reproducibility

    Returns:
        List of DataPoint objects
    """
    np.random.seed(seed)
    data = []

    # Series A: 岩性A
    x_a = np.random.lognormal(mean=2, sigma=0.5, size=num_points_per_series) * 10
    y_a = 2 * x_a + np.random.normal(loc=0, scale=25, size=num_points_per_series)
    z_a = np.random.uniform(1800, 2200, size=num_points_per_series)
    error_x_a = x_a * np.random.uniform(0.05, 0.15, size=num_points_per_series)
    error_y_a = y_a * np.random.uniform(0.05, 0.1, size=num_points_per_series)

    for i in range(num_points_per_series):
        data.append(DataPoint(
            x=x_a[i], y=y_a[i], z=z_a[i],
            error_x=error_x_a[i], error_y=error_y_a[i],
            series_id='岩性A'
        ))

    # Series B: 岩性B
    x_b = np.random.lognormal(mean=2.5, sigma=0.6, size=num_points_per_series) * 10
    y_b = 1.5 * x_b + np.random.normal(loc=50, scale=30, size=num_points_per_series)
    z_b = np.random.uniform(2000, 2500, size=num_points_per_series)
    error_x_b = x_b * np.random.uniform(0.05, 0.2, size=num_points_per_series)
    error_y_b = y_b * np.random.uniform(0.05, 0.12, size=num_points_per_series)

    for i in range(num_points_per_series):
        data.append(DataPoint(
            x=x_b[i], y=y_b[i], z=z_b[i],
            error_x=error_x_b[i], error_y=error_y_b[i],
            series_id='岩性B'
        ))

    return data


# ==============================================================================
# Section: Convenience Functions
# ==============================================================================

def create_crossplot(config: Union[CrossPlotConfig, Dict[str, Any]],
                    data: List[DataPoint]) -> go.Figure:
    """
    Convenience function to create a cross-plot.

    Args:
        config: Configuration object or dictionary
        data: List of data points

    Returns:
        Plotly Figure object
    """
    plotter = CrossPlotter(config)
    return plotter.create_plot(data)


def save_crossplot(fig: go.Figure, filename: str, **kwargs) -> None:
    """
    Save a cross-plot figure to file.

    Args:
        fig: Plotly Figure object
        filename: Output filename
        **kwargs: Additional arguments for fig.write_image()
    """
    # Set default parameters
    default_kwargs = {
        'width': 800,
        'height': 600,
        'scale': 2
    }
    default_kwargs.update(kwargs)

    fig.write_image(filename, **default_kwargs)


# ==============================================================================
# Section: Example Generation
# ==============================================================================

if __name__ == "__main__":
    print("Generating comprehensive cross-plot examples...")

    # Create output directory
    # 获取当前Python文件所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    output_dir = os.path.join(current_dir, "crossplot_output_plotly6_augment")
    os.makedirs(output_dir, exist_ok=True)
    print(f"Output directory '{output_dir}' is ready.")

    # Generate sample data
    sample_data = generate_sample_data()

    # =========================================================================
    # Core Functionality Regression Tests
    # =========================================================================

    # Example 1: Log-scale histogram regression test (most important)
    print("Generating example_log_hist_regression.png...")
    config1 = CrossPlotConfig(
        figure=FigureConfig(title="Regression Test: Log-Scale with Marginal Histograms"),
        xaxis=AxisConfig(
            title=AxisTitleConfig(text="Permeability (mD)"),
            scale=ScaleType.LOG,
        ),
        yaxis=AxisConfig(
            title=AxisTitleConfig(text="Porosity (%)"),
            scale=ScaleType.LINEAR,
        ),
        marginal=MarginalConfig(
            enabled=MarginalEnabled.BOTH,
            kind=MarginalKind.HIST,
            bins=25,
            size_x=0.12,  # 更小的边缘图高度
            size_y=0.12   # 更小的边缘图宽度
        ),
        series=[
            SeriesConfig(id='岩性A', marker=MarkerConfig(facecolor='#1f77b4')),
            SeriesConfig(id='岩性B', marker=MarkerConfig(facecolor='#ff7f0e'))
        ]
    )
    fig1 = create_crossplot(config1, sample_data)
    save_crossplot(fig1, os.path.join(output_dir, "example_log_hist_regression.png"),
                  width=800, height=800)

    # Example 2: Log scale test
    print("Generating example_log_scale.png...")
    config2 = CrossPlotConfig(
        figure=FigureConfig(title="Log Scale with Marginal Plots"),
        xaxis=AxisConfig(
            title=AxisTitleConfig(text="Acoustic Impedance (m/s * g/cc)"),
            scale=ScaleType.LOG,
        ),
        yaxis=AxisConfig(
            title=AxisTitleConfig(text="Vp/Vs Ratio"),
            scale=ScaleType.LINEAR,
        ),
        marginal=MarginalConfig(
            enabled=MarginalEnabled.BOTH,
            kind=MarginalKind.BOX,
            size_x=0.12,
            size_y=0.12
        ),
        series=[
            SeriesConfig(id='岩性A'),
            SeriesConfig(id='岩性B')
        ]
    )
    fig2 = create_crossplot(config2, sample_data)
    save_crossplot(fig2, os.path.join(output_dir, "example_log_scale.png"))

    # =========================================================================
    # Marginal Plot Type Tests
    # =========================================================================

    # Example 3: KDE marginal plots
    print("Generating example_kde_marginal.png...")
    config3 = CrossPlotConfig(
        figure=FigureConfig(title="KDE Marginal Distributions"),
        xaxis=AxisConfig(title=AxisTitleConfig(text="GR (API)")),
        yaxis=AxisConfig(title=AxisTitleConfig(text="Resistivity (ohm.m)")),
        marginal=MarginalConfig(
            enabled=MarginalEnabled.BOTH,
            kind=MarginalKind.KDE,
            size_x=0.10,  # KDE图可以更小一些
            size_y=0.10
        ),
        series=[
            SeriesConfig(id='岩性A', marker=MarkerConfig(facecolor='#2ca02c')),
            SeriesConfig(id='岩性B', marker=MarkerConfig(facecolor='#d62728'))
        ]
    )
    fig3 = create_crossplot(config3, sample_data)
    save_crossplot(fig3, os.path.join(output_dir, "example_kde_marginal.png"))

    # Example 4: Violin marginal plots
    print("Generating example_violin_marginal.png...")
    config4 = CrossPlotConfig(
        figure=FigureConfig(title="Violin Marginal Distributions"),
        xaxis=AxisConfig(title=AxisTitleConfig(text="Neutron Porosity (%)")),
        yaxis=AxisConfig(title=AxisTitleConfig(text="Bulk Density (g/cc)")),
        marginal=MarginalConfig(
            enabled=MarginalEnabled.BOTH,
            kind=MarginalKind.VIOLIN
        ),
        series=[
            SeriesConfig(id='岩性A'),
            SeriesConfig(id='岩性B')
        ]
    )
    fig4 = create_crossplot(config4, sample_data)
    save_crossplot(fig4, os.path.join(output_dir, "example_violin_marginal.png"))

    # Example 5: Single marginal (X-axis only)
    print("Generating example_single_marginal.png...")
    config5 = CrossPlotConfig(
        figure=FigureConfig(title="Single X-Axis Marginal Distribution"),
        xaxis=AxisConfig(title=AxisTitleConfig(text="Photoelectric Factor")),
        yaxis=AxisConfig(title=AxisTitleConfig(text="Gamma Ray (API)")),
        marginal=MarginalConfig(
            enabled=MarginalEnabled.X,
            kind=MarginalKind.HIST
        ),
        series=[
            SeriesConfig(id='岩性A'),
            SeriesConfig(id='岩性B')
        ]
    )
    fig5 = create_crossplot(config5, sample_data)
    save_crossplot(fig5, os.path.join(output_dir, "example_single_marginal.png"))

    # =========================================================================
    # Series Type and Feature Tests
    # =========================================================================

    # Example 6: Line-only series
    print("Generating example_line_only.png...")
    config6 = CrossPlotConfig(
        figure=FigureConfig(title="Line-Only Series Visualization"),
        xaxis=AxisConfig(title=AxisTitleConfig(text="Depth (m)")),
        yaxis=AxisConfig(title=AxisTitleConfig(text="Temperature (°C)")),
        series=[
            SeriesConfig(
                id='岩性A',
                type=SeriesType.LINE,
                line=LineConfig(color='#1f77b4', width=2.5, style='solid')
            ),
            SeriesConfig(
                id='岩性B',
                type=SeriesType.LINE,
                line=LineConfig(color='#ff7f0e', width=2.5, style='dash')
            )
        ]
    )
    fig6 = create_crossplot(config6, sample_data)
    save_crossplot(fig6, os.path.join(output_dir, "example_line_only.png"))

    # Example 7: Line and scatter combined (with tight layout)
    print("Generating example_line_and_scatter.png...")
    config7 = CrossPlotConfig(
        figure=FigureConfig(title="Combined Line and Scatter (Tight Layout)"),
        xaxis=AxisConfig(
            title=AxisTitleConfig(text="Pressure (psi)"),
            range=AxisRangeConfig(mode="tozero")  # 包含零点，减少空白
        ),
        yaxis=AxisConfig(
            title=AxisTitleConfig(text="Flow Rate (bbl/day)"),
            range=AxisRangeConfig(mode="tozero")  # 包含零点，减少空白
        ),
        series=[
            SeriesConfig(
                id='岩性A',
                type=SeriesType.BOTH,
                marker=MarkerConfig(symbol='circle', size=8),
                line=LineConfig(width=1.5)
            ),
            SeriesConfig(
                id='岩性B',
                type=SeriesType.BOTH,
                marker=MarkerConfig(symbol='square', size=8),
                line=LineConfig(width=1.5, style='dash')
            )
        ]
    )
    fig7 = create_crossplot(config7, sample_data)
    save_crossplot(fig7, os.path.join(output_dir, "example_line_and_scatter.png"))

    # Example 8: Colorbar with error bars (horizontal colorbar to avoid legend overlap)
    print("Generating example_colorbar_with_errors.png...")
    config8 = CrossPlotConfig(
        figure=FigureConfig(title="Colorbar and Error Bars (Smart Layout)"),
        xaxis=AxisConfig(title=AxisTitleConfig(text="Porosity (%)")),
        yaxis=AxisConfig(title=AxisTitleConfig(text="Permeability (mD)"), scale=ScaleType.LOG),
        colorbar=ColorbarConfig(
            visible=True,
            title="Depth (m)",
            cmap="Plasma",
            orientation="horizontal"  # Use horizontal to avoid legend overlap
        ),
        error_bars=ErrorBarConfig(visible=True),
        series=[
            SeriesConfig(id='岩性A'),
            SeriesConfig(id='岩性B')
        ]
    )
    fig8 = create_crossplot(config8, sample_data)
    save_crossplot(fig8, os.path.join(output_dir, "example_colorbar_with_errors.png"))

    # =========================================================================
    # Style and Theme Tests
    # =========================================================================

    # Example 9: Style override
    print("Generating example_style_override.png...")
    config9 = CrossPlotConfig(
        figure=FigureConfig(title="Custom Style Override"),
        xaxis=AxisConfig(title=AxisTitleConfig(text="Saturation (%)")),
        yaxis=AxisConfig(title=AxisTitleConfig(text="Capillary Pressure (psi)")),
        marginal=MarginalConfig(
            enabled=MarginalEnabled.BOTH,
            kind=MarginalKind.HIST
        ),
        series=[
            SeriesConfig(
                id='岩性A',
                marker=MarkerConfig(
                    symbol='diamond',
                    facecolor='#e74c3c',
                    size=12,
                    edgewidth=2,
                    linecolor='#c0392b'
                )
            ),
            SeriesConfig(
                id='岩性B',
                marker=MarkerConfig(
                    symbol='star',
                    facecolor='#3498db',
                    size=14,
                    edgewidth=1.5,
                    linecolor='#2980b9'
                )
            )
        ]
    )
    fig9 = create_crossplot(config9, sample_data)
    save_crossplot(fig9, os.path.join(output_dir, "example_style_override.png"))

    # Example 10: Reference line with custom slope and intercept
    print("Generating example_reference_line.png...")
    config10 = CrossPlotConfig(
        figure=FigureConfig(title="Cross-Plot with Custom Reference Lines"),
        xaxis=AxisConfig(title=AxisTitleConfig(text="Measured Value")),
        yaxis=AxisConfig(title=AxisTitleConfig(text="Predicted Value")),
        reference_line=ReferenceLineConfig(
            visible=True,
            slope=1.0,      # 1:1 line
            intercept=0.0,  # 通过原点
            color='#e74c3c',
            width=2.0,
            style='dash',
            name='1:1 Reference',
            show_legend=True
        ),
        series=[
            SeriesConfig(id='岩性A', marker=MarkerConfig(facecolor='#2ecc71')),
            SeriesConfig(id='岩性B', marker=MarkerConfig(facecolor='#e67e22'))
        ]
    )
    fig10 = create_crossplot(config10, sample_data)
    save_crossplot(fig10, os.path.join(output_dir, "example_reference_line.png"))

    # Example 11: Dark theme
    print("Generating example_dark_theme.png...")
    config11 = CrossPlotConfig(
        figure=FigureConfig(title="Dark Theme Demonstration"),
        xaxis=AxisConfig(title=AxisTitleConfig(text="X Parameter")),
        yaxis=AxisConfig(title=AxisTitleConfig(text="Y Parameter")),
        marginal=MarginalConfig(
            enabled=MarginalEnabled.BOTH,
            kind=MarginalKind.KDE
        ),
        style=get_dark_theme(),
        series=[
            SeriesConfig(id='岩性A'),
            SeriesConfig(id='岩性B')
        ]
    )
    fig11 = create_crossplot(config11, sample_data)
    save_crossplot(fig11, os.path.join(output_dir, "example_dark_theme.png"))

    # Example 12: Multiple reference lines (regression line)
    print("Generating example_regression_line.png...")
    config12 = CrossPlotConfig(
        figure=FigureConfig(title="Cross-Plot with Regression Reference Line"),
        xaxis=AxisConfig(title=AxisTitleConfig(text="Porosity (%)")),
        yaxis=AxisConfig(title=AxisTitleConfig(text="Permeability (mD)"), scale=ScaleType.LOG),
        reference_line=ReferenceLineConfig(
            visible=True,
            slope=2.5,      # 假设的回归斜率
            intercept=50.0, # 假设的回归截距
            color='#9b59b6',
            width=2.5,
            style='solid',
            name='Regression Line',
            show_legend=True
        ),
        series=[
            SeriesConfig(id='岩性A', marker=MarkerConfig(facecolor='#3498db', size=10)),
            SeriesConfig(id='岩性B', marker=MarkerConfig(facecolor='#e67e22', size=10))
        ]
    )
    fig12 = create_crossplot(config12, sample_data)
    save_crossplot(fig12, os.path.join(output_dir, "example_regression_line.png"))

    # Example 13: Vertical colorbar with smart legend positioning
    print("Generating example_smart_legend_layout.png...")
    config13 = CrossPlotConfig(
        figure=FigureConfig(title="Smart Legend Layout with Vertical Colorbar"),
        xaxis=AxisConfig(title=AxisTitleConfig(text="Neutron Porosity (%)")),
        yaxis=AxisConfig(title=AxisTitleConfig(text="Bulk Density (g/cc)")),
        colorbar=ColorbarConfig(
            visible=True,
            title="Gamma Ray (API)",
            cmap="Viridis",
            orientation="vertical"  # Vertical colorbar with legend repositioned
        ),
        series=[
            SeriesConfig(id='岩性A', marker=MarkerConfig(size=12)),
            SeriesConfig(id='岩性B', marker=MarkerConfig(size=12))
        ]
    )
    fig13 = create_crossplot(config13, sample_data)
    save_crossplot(fig13, os.path.join(output_dir, "example_smart_legend_layout.png"))

    # Example 14: Custom marginal plot sizes
    print("Generating example_custom_marginal_sizes.png...")
    config14 = CrossPlotConfig(
        figure=FigureConfig(title="Custom Marginal Plot Sizes (Compact Layout)"),
        xaxis=AxisConfig(title=AxisTitleConfig(text="Acoustic Impedance")),
        yaxis=AxisConfig(title=AxisTitleConfig(text="Vp/Vs Ratio")),
        marginal=MarginalConfig(
            enabled=MarginalEnabled.BOTH,
            kind=MarginalKind.HIST,
            size_x=0.08,    # 很小的X边缘图高度 (8%)
            size_y=0.08,    # 很小的Y边缘图宽度 (8%)
            spacing=0.005   # 更小的间距
        ),
        series=[
            SeriesConfig(id='岩性A', marker=MarkerConfig(facecolor='#3498db')),
            SeriesConfig(id='岩性B', marker=MarkerConfig(facecolor='#e74c3c'))
        ]
    )
    fig14 = create_crossplot(config14, sample_data)
    save_crossplot(fig14, os.path.join(output_dir, "example_custom_marginal_sizes.png"))

    # Example 15: Range mode comparison
    print("Generating example_range_mode_comparison.png...")
    config15 = CrossPlotConfig(
        figure=FigureConfig(title="Range Mode Comparison (tozero vs normal)"),
        xaxis=AxisConfig(
            title=AxisTitleConfig(text="X Data"),
            range=AxisRangeConfig(mode="normal")  # 自动范围（有空白）
        ),
        yaxis=AxisConfig(
            title=AxisTitleConfig(text="Y Data"),
            range=AxisRangeConfig(mode="tozero")  # 包含零点（紧凑）
        ),
        series=[
            SeriesConfig(id='岩性A', marker=MarkerConfig(facecolor='#3498db')),
            SeriesConfig(id='岩性B', marker=MarkerConfig(facecolor='#e74c3c'))
        ]
    )
    fig15 = create_crossplot(config15, sample_data)
    save_crossplot(fig15, os.path.join(output_dir, "example_range_mode_comparison.png"))

    # Example 16: Advanced tick control demonstration
    print("Generating example_advanced_tick_control.png...")
    config16 = CrossPlotConfig(
        figure=FigureConfig(title="Advanced Tick Control (Major + Minor Ticks)"),
        xaxis=AxisConfig(
            title=AxisTitleConfig(text="Permeability (mD)"),
            ticks=TickConfig(
                mode="linear",
                interval=200,        # 主刻度间隔200
                start=0,            # 从0开始
                position="outside",
                # 次要刻度配置
                minor_enabled=True,
                minor_interval=100,  # 次要刻度间隔100
                minor_position="outside"
            ),
            grid=GridConfig(
                major_enabled=True,
                minor_enabled=True,
                minor_dash="dot"
            )
        ),
        yaxis=AxisConfig(
            title=AxisTitleConfig(text="Flow Rate (bbl/day)"),
            ticks=TickConfig(
                mode="linear",
                interval=200,        # 主刻度间隔200 (适合0~1000范围)
                start=0,
                position="outside",
                # 次要刻度配置
                minor_enabled=True,
                minor_interval=100,  # 次要刻度间隔100
                minor_position="outside"
            ),
            grid=GridConfig(
                major_enabled=True,
                minor_enabled=True,
                minor_dash="dot"
            )
        ),
        series=[
            SeriesConfig(id='岩性A', marker=MarkerConfig(facecolor='#1f77b4')),
            SeriesConfig(id='岩性B', marker=MarkerConfig(facecolor='#ff7f0e'))
        ]
    )
    fig16 = create_crossplot(config16, sample_data)
    save_crossplot(fig16, os.path.join(output_dir, "example_advanced_tick_control.png"))

    # Example 17: Advanced marginal plot configuration
    print("Generating example_advanced_marginal_config.png...")
    config17 = CrossPlotConfig(
        figure=FigureConfig(title="Advanced Marginal Plot Configuration (Plotly 6.0)"),
        xaxis=AxisConfig(title=AxisTitleConfig(text="Porosity (%)")),
        yaxis=AxisConfig(title=AxisTitleConfig(text="Permeability (mD)"), scale=ScaleType.LOG),
        marginal=MarginalConfig(
            enabled=MarginalEnabled.BOTH,
            kind=MarginalKind.VIOLIN,
            size_x=0.2,
            size_y=0.2,
            # 高级配置展示
            violin_style=ViolinStyleConfig(
                opacity=0.8,
                line_width=2.0,
                points="all",        # 显示所有数据点
                box_visible=True,    # 显示内部箱线图
                meanline_visible=True, # 显示均值线
                side="both"          # 双侧显示
            ),
            color_sync=True,
            linked_selection=True,
            gap_x=0.03,
            gap_y=0.03
        ),
        series=[
            SeriesConfig(id='岩性A', marker=MarkerConfig(facecolor='#1f77b4')),
            SeriesConfig(id='岩性B', marker=MarkerConfig(facecolor='#ff7f0e'))
        ]
    )
    fig17 = create_crossplot(config17, sample_data)
    save_crossplot(fig17, os.path.join(output_dir, "example_advanced_marginal_config.png"))

    # Example 18: Dual colorbar example - 展示多系列独立色轴功能
    # 符合《交会图绘制要求》1.3节："允许多 Series 共享色轴或各自独立色轴"
    print("Generating example_dual_colorbar.png...")

    # 为双颜色轴示例生成特殊数据
    def generate_dual_colorbar_data():
        np.random.seed(42)
        n_points = 100  # 每个系列100个点
        data = []

        # 系列A：温度数据 (0-100°C)
        x_a = np.random.normal(15, 5, n_points)  # 孔隙度
        y_a = np.random.lognormal(2, 1, n_points)  # 渗透率
        temp_a = 20 + 0.5 * x_a + 0.1 * np.log(y_a) + np.random.normal(0, 5, n_points)  # 温度
        temp_a = np.clip(temp_a, 0, 100)

        for i in range(n_points):
            data.append(DataPoint(
                x=x_a[i], y=y_a[i], z=temp_a[i],
                series_id='温度系列'
            ))

        # 系列B：压力数据 (1000-5000 psi)
        x_b = np.random.normal(20, 4, n_points)  # 孔隙度
        y_b = np.random.lognormal(3, 0.8, n_points)  # 渗透率
        pressure_b = 2000 + 50 * x_b + 20 * np.log(y_b) + np.random.normal(0, 200, n_points)  # 压力
        pressure_b = np.clip(pressure_b, 1000, 5000)

        for i in range(n_points):
            data.append(DataPoint(
                x=x_b[i], y=y_b[i], z=pressure_b[i],
                series_id='压力系列'
            ))

        return data

    dual_data = generate_dual_colorbar_data()

    config18 = CrossPlotConfig(
        figure=FigureConfig(
            title="Dual Colorbar Example - Temperature vs Pressure",
            size=(900, 600)  # 稍微宽一些以容纳两个颜色轴
        ),
        xaxis=AxisConfig(title=AxisTitleConfig(text="Porosity (%)")),
        yaxis=AxisConfig(
            title=AxisTitleConfig(text="Permeability (mD)"),
            scale=ScaleType.LOG
        ),
        series=[
            SeriesConfig(
                id='温度系列',
                marker=MarkerConfig(facecolor='#1f77b4', size=8),
                type=SeriesType.SCATTER,
                # 第一个系列的独立颜色轴配置
                colorbar=ColorbarConfig(
                    visible=True,
                    cmap="Viridis",  # 温度用冷暖色调
                    title="Temperature (°C)",
                    orientation="vertical",
                    x=1.02,  # 第一个颜色轴位置
                    y=0.5,   # 垂直居中
                    yanchor="middle",  # 以中心点为锚点
                    thickness=15,
                    len=0.8
                ),
                colorscale="Viridis"
            ),
            SeriesConfig(
                id='压力系列',
                marker=MarkerConfig(facecolor='#ff7f0e', size=8),
                type=SeriesType.SCATTER,
                # 第二个系列的独立颜色轴配置
                colorbar=ColorbarConfig(
                    visible=True,
                    cmap="Plasma",  # 压力用不同色调
                    title="Pressure (psi)",
                    orientation="vertical",
                    x=1.12,  # 第二个颜色轴位置（更右边）
                    y=0.5,   # 垂直居中
                    yanchor="middle",  # 以中心点为锚点
                    thickness=15,
                    len=0.8
                ),
                colorscale="Plasma"
            )
        ],
        # 不使用全局颜色轴，每个系列有独立的颜色轴
        colorbar=ColorbarConfig(visible=False),
        marginal=MarginalConfig(
            enabled=MarginalEnabled.BOTH,
            kind=MarginalKind.HIST,
            size_x=0.15,
            size_y=0.15
        )
    )

    # 注意：这个示例需要特殊的数据处理来支持双颜色轴
    # 在实际实现中，可能需要扩展create_crossplot函数来支持多个颜色轴
    fig18 = create_crossplot(config18, dual_data)
    save_crossplot(fig18, os.path.join(output_dir, "example_dual_colorbar.png"))

    print(f"\nAll examples generated successfully in '{output_dir}' directory!")
    print("\nGenerated files:")
    for filename in sorted(os.listdir(output_dir)):
        if filename.endswith('.png'):
            print(f"  - {filename}")

    print("\nProduction-grade cross-plot module is ready for use!")
    print("See module docstring for comprehensive feature documentation.")
